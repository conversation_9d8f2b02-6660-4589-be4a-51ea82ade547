package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

const (
	url                    = "http://192.168.1.56:9977"
	HEART_BEAT             = 5005
	RECV_TXT_MSG           = 1
	RECV_PIC_MSG           = 3
	USER_LIST              = 5000
	GET_USER_LIST_SUCCSESS = 5001
	GET_USER_LIST_FAIL     = 5002
	TXT_MSG                = 555
	PIC_MSG                = 500
	AT_MSG                 = 550
	CHATROOM_MEMBER        = 5010
	CHATROOM_MEMBER_NICK   = 5020
	PERSONAL_INFO          = 6500
	DEBUG_SWITCH           = 6000
	PERSONAL_DETAIL        = 6550
	ATTATCH_FILE           = 5003
)

type RequestBody struct {
	Para map[string]interface{} `json:"para"`
}

func getID() string {
	return fmt.Sprintf("%d", time.Now().UnixNano())
}

func sendRequest(api string, para map[string]interface{}) (string, error) {
	requestBody := RequestBody{
		Para: para,
	}
	jsonBody, err := json.Marshal(requestBody)
	if err != nil {
		return "", err
	}

	resp, err := http.Post(url+api, "application/json", bytes.NewBuffer(jsonBody))
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	// var response Response
	// err = json.Unmarshal(body, &response)
	// if err != nil {
	// 	return "", err
	// }

	return string(body), nil
}

func sendAtMsg() (string, error) {
	para := map[string]interface{}{
		"id":       getID(),
		"type":     AT_MSG,
		"roomid":   "23023281066@chatroom",
		"wxid":     "zhanghua_cd",
		"content":  "at msg test,hello world，真的有一套",
		"nickname": "老张",
		"ext":      "null",
	}

	return sendRequest("/api/sendatmsg", para)
}

func sendPic() (string, error) {
	para := map[string]interface{}{
		"id":       getID(),
		"type":     PIC_MSG,
		"wxid":     "23023281066@chatroom",
		"roomid":   "null",
		"content":  "C:\\tmp\\2.jpg",
		"nickname": "null",
		"ext":      "null",
	}

	return sendRequest("/api/sendpic", para)
}

func getMemberNick(wxID, roomID string) (string, error) {
	para := map[string]interface{}{
		"id":       getID(),
		"type":     CHATROOM_MEMBER_NICK,
		"wxid":     wxID,
		"roomid":   roomID,
		"content":  "null",
		"nickname": "null",
		"ext":      "null",
	}

	return sendRequest("/api/getmembernick", para)
}

func getMemberID() (string, error) {
	para := map[string]interface{}{
		"id":      getID(),
		"type":    CHATROOM_MEMBER,
		"wxid":    "null",
		"content": "op:list member",
	}

	return sendRequest("/api/getmemberid", para)
}

func getContactList() (string, error) {
	para := map[string]interface{}{
		"id":       getID(),
		"type":     USER_LIST,
		"roomid":   "null",
		"wxid":     "null",
		"content":  "null",
		"nickname": "null",
		"ext":      "null",
	}

	return sendRequest("/api/getcontactlist", para)
}

func getChatroomMemberList() (string, error) {
	para := map[string]interface{}{
		"id":       getID(),
		"type":     CHATROOM_MEMBER,
		"roomid":   "null",
		"wxid":     "null",
		"content":  "null",
		"nickname": "null",
		"ext":      "null",
	}

	return sendRequest("/api/get_charroom_member_list", para)
}

func sendTxtMsg(wxid, content string) (string, error) {
	para := map[string]interface{}{
		"id":       getID(),
		"type":     TXT_MSG,
		"wxid":     wxid,
		"roomid":   "null",
		"content":  content,
		"nickname": "null",
		"ext":      "null",
	}

	return sendRequest("/api/sendtxtmsg", para)
}

func sendAttatch() (string, error) {
	para := map[string]interface{}{
		"id":       getID(),
		"type":     ATTATCH_FILE,
		"wxid":     "23023281066@chatroom",
		"roomid":   "null",
		"content":  "C:\\tmp\\log.7z",
		"nickname": "null",
		"ext":      "null",
	}

	return sendRequest("/api/sendattatch", para)
}

// func main() {
// 	// response, err := getContactList()
// 	// response, err := getChatroomMemberList()
// 	response, err := sendTxtMsg("SoNice003", "ddddd")
// 	// response, err := getMemberNick("zhanghua_cd", "23023281066@chatroom")
// 	// response, err := sendAtMsg()
// 	// response, err := sendAttatch()
// 	// response, err := getMemberNick("zhanghua_cd", "23023281066@chatroom")
// 	if err != nil {
// 		fmt.Println("Error:", err)
// 		return
// 	}

// 	fmt.Println(response)
// }
