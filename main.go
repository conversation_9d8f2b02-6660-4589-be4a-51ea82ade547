package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"time"

	"github.com/gorilla/websocket"
)

// 消息结构体
type Message struct {
	PostType    string `json:"post_type"`
	MessageType string `json:"message_type"`
	SelfID      int64  `json:"self_id"`
	UserID      int64  `json:"user_id"`
	Time        int64  `json:"time"`
	MessageID   int64  `json:"message_id"`
	RawMessage  string `json:"raw_message"`
	Sender      struct {
		UserID   int64  `json:"user_id"`
		Nickname string `json:"nickname"`
		Card     string `json:"card"`
	} `json:"sender"`
	MessageArray []struct {
		Type string `json:"type"`
		Data struct {
			Text     string `json:"text,omitempty"`      // 文本消息
			File     string `json:"file,omitempty"`      // 图片/文件名
			URL      string `json:"url,omitempty"`       // 图片URL
			FileSize string `json:"file_size,omitempty"` // 文件大小
			FileID   string `json:"file_id,omitempty"`   // 文件ID
		} `json:"data"`
	} `json:"message"`
}

// 日志文件
var logFile *os.File

// 初始化日志文件
func initLogFile() error {
	var err error
	logFile, err = os.OpenFile("ws_messages.log", os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		return err
	}
	return nil
}

// 写入日志
func writeLog(message string) {
	timestamp := time.Now().Format("2006-01-02 15:04:05")
	logEntry := fmt.Sprintf("[%s] %s\n", timestamp, message)

	// 控制台输出
	fmt.Print(logEntry)

	// 文件输出
	if logFile != nil {
		logFile.WriteString(logEntry)
		logFile.Sync()
	}
}

// 解析消息
func parseMessage(data []byte, clientAddr string) {
	var msg Message
	err := json.Unmarshal(data, &msg)
	if err != nil {
		writeLog(fmt.Sprintf("%s 解析JSON失败: %v", clientAddr, err))
		writeLog(fmt.Sprintf("%s 原始数据: %s", clientAddr, string(data)))
		return
	}

	// 只处理消息类型的数据
	if msg.PostType != "message" {
		writeLog(fmt.Sprintf("%s 非消息类型: %s", clientAddr, msg.PostType))
		return
	}

	nickname := msg.Sender.Nickname
	if nickname == "" {
		nickname = fmt.Sprintf("用户%d", msg.UserID)
	}

	// 根据消息内容分类处理
	if len(msg.MessageArray) > 0 {
		msgType := msg.MessageArray[0].Type
		switch msgType {
		case "text":
			text := msg.MessageArray[0].Data.Text
			writeLog(fmt.Sprintf("%s [文本] %s: %s", clientAddr, nickname, text))

		case "image":
			fileName := msg.MessageArray[0].Data.File
			fileSize := msg.MessageArray[0].Data.FileSize
			url := msg.MessageArray[0].Data.URL
			writeLog(fmt.Sprintf("%s [图片] %s: %s (%s字节) %s", clientAddr, nickname, fileName, fileSize, url))

		case "file":
			fileName := msg.MessageArray[0].Data.File
			fileSize := msg.MessageArray[0].Data.FileSize
			fileID := msg.MessageArray[0].Data.FileID
			writeLog(fmt.Sprintf("%s [文件] %s: %s (%s字节) ID:%s", clientAddr, nickname, fileName, fileSize, fileID))

		default:
			writeLog(fmt.Sprintf("%s [%s] %s: %s", clientAddr, msgType, nickname, msg.RawMessage))
		}
	} else {
		writeLog(fmt.Sprintf("%s [未知] %s: %s", clientAddr, nickname, msg.RawMessage))
	}
}

// WebSocket升级器
var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		// 允许所有来源的连接（生产环境中应该更严格）
		return true
	},
}

// 处理WebSocket连接
func handleWebSocket(w http.ResponseWriter, r *http.Request) {
	// 升级HTTP连接为WebSocket连接
	conn, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Printf("WebSocket升级失败: %v", err)
		return
	}
	defer conn.Close()

	// 获取客户端地址
	clientAddr := conn.RemoteAddr().String()
	writeLog(fmt.Sprintf("%s 连接建立", clientAddr))

	// 循环读取消息
	for {
		// 读取消息
		_, message, err := conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				writeLog(fmt.Sprintf("%s 连接异常关闭: %v", clientAddr, err))
			} else {
				writeLog(fmt.Sprintf("%s 连接关闭", clientAddr))
			}
			break
		}

		// 解析并打印接收到的报文
		parseMessage(message, clientAddr)

		// 可选：回复确认消息（注释掉以减少噪音）
		// response := fmt.Sprintf("OK")
		// err = conn.WriteMessage(websocket.TextMessage, []byte(response))
		// if err != nil {
		// 	fmt.Printf("[%s] %s 发送回复失败: %v\n", time.Now().Format("15:04:05"), clientAddr, err)
		// 	break
		// }
	}
}

func main() {
	// 初始化日志文件
	err := initLogFile()
	if err != nil {
		log.Fatalf("初始化日志文件失败: %v", err)
	}
	defer logFile.Close()

	// 设置路由
	http.HandleFunc("/ws", handleWebSocket)

	// 启动服务器
	port := ":8081"
	writeLog("WebSocket服务器启动中...")
	writeLog(fmt.Sprintf("WebSocket地址: ws://localhost%s/ws", port))
	writeLog("按 Ctrl+C 停止服务器")

	log.Fatal(http.ListenAndServe(port, nil))
}
