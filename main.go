package main

import (
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/gorilla/websocket"
)

// WebSocket升级器
var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		// 允许所有来源的连接（生产环境中应该更严格）
		return true
	},
}

// 处理WebSocket连接
func handleWebSocket(w http.ResponseWriter, r *http.Request) {
	// 升级HTTP连接为WebSocket连接
	conn, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Printf("WebSocket升级失败: %v", err)
		return
	}
	defer conn.Close()

	// 获取客户端地址
	clientAddr := conn.RemoteAddr().String()
	log.Printf("新的WebSocket连接: %s", clientAddr)

	// 循环读取消息
	for {
		// 读取消息
		messageType, message, err := conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("WebSocket连接异常关闭 [%s]: %v", clientAddr, err)
			} else {
				log.Printf("WebSocket连接关闭 [%s]: %v", clientAddr, err)
			}
			break
		}

		// 打印接收到的报文
		timestamp := time.Now().Format("2006-01-02 15:04:05")
		fmt.Printf("\n=== 收到报文 ===\n")
		fmt.Printf("时间: %s\n", timestamp)
		fmt.Printf("客户端: %s\n", clientAddr)
		fmt.Printf("消息类型: %s\n", getMessageTypeName(messageType))
		fmt.Printf("消息长度: %d 字节\n", len(message))
		fmt.Printf("消息内容: %s\n", string(message))
		fmt.Printf("原始字节: %v\n", message)
		fmt.Printf("================\n\n")

		// 可选：回复确认消息
		response := fmt.Sprintf("服务器已收到消息，时间: %s", timestamp)
		err = conn.WriteMessage(websocket.TextMessage, []byte(response))
		if err != nil {
			log.Printf("发送回复消息失败 [%s]: %v", clientAddr, err)
			break
		}
	}
}

// 获取消息类型名称
func getMessageTypeName(messageType int) string {
	switch messageType {
	case websocket.TextMessage:
		return "文本消息"
	case websocket.BinaryMessage:
		return "二进制消息"
	case websocket.CloseMessage:
		return "关闭消息"
	case websocket.PingMessage:
		return "Ping消息"
	case websocket.PongMessage:
		return "Pong消息"
	default:
		return fmt.Sprintf("未知类型(%d)", messageType)
	}
}

// 主页处理器
func handleHome(w http.ResponseWriter, r *http.Request) {
	html := `
<!DOCTYPE html>
<html>
<head>
    <title>WebSocket测试页面</title>
    <meta charset="UTF-8">
</head>
<body>
    <h1>WebSocket测试客户端</h1>
    <div>
        <button id="connect">连接</button>
        <button id="disconnect">断开</button>
        <span id="status">未连接</span>
    </div>
    <div>
        <input type="text" id="messageInput" placeholder="输入消息" style="width: 300px;">
        <button id="send">发送</button>
    </div>
    <div>
        <h3>接收到的消息:</h3>
        <div id="messages" style="border: 1px solid #ccc; height: 300px; overflow-y: scroll; padding: 10px;"></div>
    </div>

    <script>
        let ws = null;
        const status = document.getElementById('status');
        const messages = document.getElementById('messages');
        const messageInput = document.getElementById('messageInput');

        document.getElementById('connect').onclick = function() {
            if (ws) return;
            
            ws = new WebSocket('ws://localhost:8081/ws');
            
            ws.onopen = function() {
                status.textContent = '已连接';
                status.style.color = 'green';
                addMessage('连接成功');
            };
            
            ws.onmessage = function(event) {
                addMessage('收到: ' + event.data);
            };
            
            ws.onclose = function() {
                status.textContent = '已断开';
                status.style.color = 'red';
                addMessage('连接关闭');
                ws = null;
            };
            
            ws.onerror = function(error) {
                addMessage('错误: ' + error);
            };
        };

        document.getElementById('disconnect').onclick = function() {
            if (ws) {
                ws.close();
            }
        };

        document.getElementById('send').onclick = function() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                const message = messageInput.value;
                if (message) {
                    ws.send(message);
                    addMessage('发送: ' + message);
                    messageInput.value = '';
                }
            } else {
                alert('请先连接WebSocket');
            }
        };

        messageInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.getElementById('send').click();
            }
        });

        function addMessage(message) {
            const div = document.createElement('div');
            div.textContent = new Date().toLocaleTimeString() + ' - ' + message;
            messages.appendChild(div);
            messages.scrollTop = messages.scrollHeight;
        }
    </script>
</body>
</html>`
	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	w.Write([]byte(html))
}

func main() {
	// 设置路由
	http.HandleFunc("/", handleHome)
	http.HandleFunc("/ws", handleWebSocket)

	// 启动服务器
	port := ":8081"
	fmt.Printf("WebSocket服务器启动中...\n")
	fmt.Printf("服务地址: http://localhost%s\n", port)
	fmt.Printf("WebSocket地址: ws://localhost%s/ws\n", port)
	fmt.Printf("按 Ctrl+C 停止服务器\n\n")

	log.Fatal(http.ListenAndServe(port, nil))
}
